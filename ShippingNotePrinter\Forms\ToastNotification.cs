using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace ShippingNotePrinter.Forms
{
    /// <summary>
    /// 现代化Toast通知组件
    /// </summary>
    public class ToastNotification : Form
    {
        private Label lblMessage;
        private Panel pnlIcon;
        private Timer autoCloseTimer;
        private Timer animationTimer;
        private bool isShowing = true;
        private int targetY;
        private ToastType toastType;

        public enum ToastType
        {
            Success,
            Warning,
            Error,
            Info
        }

        public ToastNotification(string message, ToastType type = ToastType.Info, int duration = 3000)
        {
            this.toastType = type;
            InitializeComponent();
            SetupToastStyle();
            SetMessage(message);
            SetupAutoClose(duration);
            SetupAnimation();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 窗体设置
            this.Text = "";
            this.Size = new Size(350, 80);
            this.FormBorderStyle = FormBorderStyle.None;
            this.ShowInTaskbar = false;
            this.TopMost = true;
            this.StartPosition = FormStartPosition.Manual;

            // 图标面板
            this.pnlIcon = new Panel
            {
                Location = new Point(15, 15),
                Size = new Size(50, 50),
                BackColor = Color.Transparent
            };

            // 消息标签
            this.lblMessage = new Label
            {
                Font = new Font("微软雅黑", 11F),
                ForeColor = Color.White,
                Location = new Point(75, 15),
                Size = new Size(260, 50),
                Text = "这是一条通知消息",
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleLeft
            };

            this.Controls.Add(this.pnlIcon);
            this.Controls.Add(this.lblMessage);

            this.ResumeLayout(false);
        }

        private void SetupToastStyle()
        {
            Color backgroundColor;
            Color iconColor;
            string iconText;

            switch (toastType)
            {
                case ToastType.Success:
                    backgroundColor = Color.FromArgb(46, 204, 113);
                    iconColor = Color.White;
                    iconText = "✓";
                    break;
                case ToastType.Warning:
                    backgroundColor = Color.FromArgb(243, 156, 18);
                    iconColor = Color.White;
                    iconText = "⚠";
                    break;
                case ToastType.Error:
                    backgroundColor = Color.FromArgb(231, 76, 60);
                    iconColor = Color.White;
                    iconText = "✖";
                    break;
                case ToastType.Info:
                default:
                    backgroundColor = Color.FromArgb(52, 152, 219);
                    iconColor = Color.White;
                    iconText = "ℹ";
                    break;
            }

            this.BackColor = backgroundColor;

            // 设置窗体圆角
            this.Paint += (s, e) =>
            {
                using (var path = GetRoundedRectanglePath(this.ClientRectangle, 8))
                {
                    this.Region = new Region(path);
                }

                // 绘制微妙的阴影
                using (var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
                {
                    var shadowRect = new Rectangle(2, 2, this.Width, this.Height);
                    using (var shadowPath = GetRoundedRectanglePath(shadowRect, 8))
                    {
                        e.Graphics.FillPath(shadowBrush, shadowPath);
                    }
                }

                // 绘制主背景
                using (var backgroundBrush = new SolidBrush(backgroundColor))
                using (var path = GetRoundedRectanglePath(this.ClientRectangle, 8))
                {
                    e.Graphics.FillPath(backgroundBrush, path);
                }
            };

            // 设置图标
            pnlIcon.Paint += (s, e) =>
            {
                // 绘制图标背景圆圈
                using (var brush = new SolidBrush(Color.FromArgb(50, 255, 255, 255)))
                {
                    e.Graphics.FillEllipse(brush, new Rectangle(0, 0, 50, 50));
                }

                // 绘制图标文字
                using (var brush = new SolidBrush(iconColor))
                using (var font = new Font("Segoe UI Symbol", 18F, FontStyle.Bold))
                {
                    var textSize = e.Graphics.MeasureString(iconText, font);
                    var x = (50 - textSize.Width) / 2;
                    var y = (50 - textSize.Height) / 2;
                    e.Graphics.DrawString(iconText, font, brush, x, y);
                }
            };

            // 添加点击关闭功能
            this.Click += (s, e) => CloseToast();
            lblMessage.Click += (s, e) => CloseToast();
            pnlIcon.Click += (s, e) => CloseToast();

            // 添加鼠标悬停效果
            this.MouseEnter += (s, e) =>
            {
                if (autoCloseTimer != null && autoCloseTimer.Enabled)
                {
                    autoCloseTimer.Stop();
                }
            };

            this.MouseLeave += (s, e) =>
            {
                if (autoCloseTimer != null)
                {
                    autoCloseTimer.Start();
                }
            };
        }

        private GraphicsPath GetRoundedRectanglePath(Rectangle rect, int radius)
        {
            var path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }

        private void SetMessage(string message)
        {
            lblMessage.Text = message;

            // 根据消息长度调整窗体大小
            using (var g = this.CreateGraphics())
            {
                var textSize = g.MeasureString(message, lblMessage.Font, 260);
                var newHeight = Math.Max(80, (int)textSize.Height + 30);
                this.Size = new Size(350, newHeight);
                lblMessage.Size = new Size(260, newHeight - 30);
            }
        }

        private void SetupAutoClose(int duration)
        {
            autoCloseTimer = new Timer
            {
                Interval = duration
            };
            autoCloseTimer.Tick += (s, e) =>
            {
                autoCloseTimer.Stop();
                CloseToast();
            };
            autoCloseTimer.Start();
        }

        private void SetupAnimation()
        {
            // 设置初始位置（屏幕右边界外）
            var screen = Screen.FromPoint(Cursor.Position);
            var startX = screen.WorkingArea.Right;
            var startY = screen.WorkingArea.Bottom - this.Height - 20;
            this.Location = new Point(startX, startY);

            // 设置目标位置
            targetY = startY;
            var targetX = screen.WorkingArea.Right - this.Width - 20;

            // 滑入动画
            animationTimer = new Timer { Interval = 20 };
            animationTimer.Tick += (s, e) =>
            {
                if (isShowing)
                {
                    // 滑入动画
                    if (this.Location.X > targetX)
                    {
                        this.Location = new Point(this.Location.X - 15, this.Location.Y);
                    }
                    else
                    {
                        this.Location = new Point(targetX, this.Location.Y);
                        animationTimer.Stop();
                    }
                }
                else
                {
                    // 滑出动画
                    if (this.Location.X < screen.WorkingArea.Right)
                    {
                        this.Location = new Point(this.Location.X + 15, this.Location.Y);
                    }
                    else
                    {
                        animationTimer.Stop();
                        this.Hide();
                        this.Dispose();
                    }
                }
            };
            animationTimer.Start();
        }

        private void CloseToast()
        {
            if (autoCloseTimer != null)
            {
                autoCloseTimer.Stop();
                autoCloseTimer.Dispose();
            }

            isShowing = false;
            if (animationTimer != null)
            {
                animationTimer.Start();
            }
        }

        // 静态方法用于显示不同类型的Toast
        public static void Show(string message, ToastType type = ToastType.Info, int duration = 3000)
        {
            var toast = new ToastNotification(message, type, duration);
            toast.Show();
        }

        public static void ShowSuccess(string message, int duration = 3000)
        {
            Show(message, ToastType.Success, duration);
        }

        public static void ShowWarning(string message, int duration = 3000)
        {
            Show(message, ToastType.Warning, duration);
        }

        public static void ShowError(string message, int duration = 3000)
        {
            Show(message, ToastType.Error, duration);
        }

        public static void ShowInfo(string message, int duration = 3000)
        {
            Show(message, ToastType.Info, duration);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                autoCloseTimer?.Dispose();
                animationTimer?.Dispose();
            }
            base.Dispose(disposing);
        }

        // 防止获得焦点
        protected override bool ShowWithoutActivation => true;

        // 防止获得焦点
        protected override CreateParams CreateParams
        {
            get
            {
                var cp = base.CreateParams;
                cp.ExStyle |= 0x08000000; // WS_EX_NOACTIVATE
                return cp;
            }
        }
    }
} 