# 发运单打印系统

## 项目概述

这是一个专为触控一体机设计的Windows发运单打印系统，主要功能包括：

1. **手机号验证**：司机输入手机号，系统发送短信验证码
2. **验证码验证**：输入短信验证码进行身份验证
3. **发运单查询**：根据手机号查询相关的发运单列表
4. **批量打印**：选择需要打印的发运单，使用针式打印机进行打印

## 技术架构

### 前端
- **Windows Forms**：触控友好的用户界面
- **.NET Framework 4.8**：稳定的运行环境
- **触控优化**：按钮和输入框针对触控操作进行了优化

### 后端服务
- **SOAP客户端**：调用Tomcat中的POJO Axis2服务
- **Oracle数据库**：发运单数据存储
- **短信服务**：验证码发送和验证

### 打印功能
- **针式打印机支持**：专为发运单格式优化
- **自定义打印格式**：完全按照提供的发运单样式设计
- **批量打印**：支持同时打印多张发运单

## 项目结构

```
ShippingNotePrinter/
├── Forms/                  # 用户界面
│   ├── MainForm.cs        # 主窗体
│   ├── MainForm.Designer.cs
│   └── MainForm.resx
├── Models/                 # 数据模型
│   ├── ShippingNote.cs    # 发运单模型
│   └── ShippingNoteItem.cs # 发运单明细项目模型
├── Services/              # 服务层
│   ├── SoapService.cs     # SOAP服务客户端
│   ├── SmsService.cs      # 短信服务
│   └── PrintService.cs    # 打印服务
├── Properties/            # 项目属性
└── App.config            # 配置文件
```

## 配置说明

### App.config 配置项

```xml
<appSettings>
    <!-- Tomcat SOAP服务配置 -->
    <add key="SoapServiceUrl" value="http://localhost:8080/axis2/services/ShippingNoteService" />
    <add key="SmsServiceUrl" value="http://localhost:8080/axis2/services/SmsService" />
    
    <!-- 打印机配置 -->
    <add key="DefaultPrinterName" value="" />
    
    <!-- 数据库连接配置 -->
    <add key="OracleConnectionString" value="Data Source=localhost:1521/XE;User Id=your_username;Password=your_password;" />
</appSettings>
```

## 使用流程

1. **启动应用**：在触控一体机上启动发运单打印系统
2. **输入手机号**：司机在第一步输入手机号码
3. **发送验证码**：点击"发送验证码"按钮，系统调用短信服务发送验证码
4. **验证身份**：在第二步输入收到的短信验证码
5. **查询发运单**：验证成功后，系统自动查询该手机号关联的发运单
6. **选择打印**：在第三步选择需要打印的发运单（支持多选）
7. **确认打印**：点击"打印发运单"按钮，系统调用针式打印机进行打印

## 依赖项

### NuGet包
- Oracle.ManagedDataAccess（Oracle数据库连接）
- System.Drawing.Printing（打印功能）
- System.Web.Services（SOAP服务调用）

### 系统要求
- Windows 7/8/10/11
- .NET Framework 4.8
- 针式打印机驱动程序
- 网络连接（访问Tomcat服务）

## 部署说明

1. **编译项目**：使用Visual Studio编译Release版本
2. **配置文件**：修改App.config中的服务地址和数据库连接字符串
3. **安装依赖**：确保目标机器安装了.NET Framework 4.8
4. **打印机设置**：安装并配置针式打印机驱动
5. **网络配置**：确保能够访问Tomcat服务器

## 待完善功能

1. **SQL查询语句**：等待您提供具体的数据库查询语句
2. **SOAP响应解析**：根据实际的服务响应格式完善解析逻辑
3. **错误处理**：增强网络异常和打印异常的处理
4. **日志记录**：添加操作日志和错误日志功能
5. **配置界面**：添加系统配置管理界面

## 联系信息

如需技术支持或有任何问题，请联系开发团队。

---

**注意**：本系统专为公元管件有限公司的发运单打印需求设计，请根据实际环境调整配置参数。
