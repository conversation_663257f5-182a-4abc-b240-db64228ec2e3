@echo off
echo 正在构建发运单打印系统...

REM 自动检测Visual Studio安装路径
set MSBUILD_PATH=""

REM 检查D盘的Visual Studio安装
echo 正在检测Visual Studio安装位置...

REM 检查D盘 VS2022
if exist "D:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="D:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到Visual Studio 2022 Professional (D盘)
    goto :found
)

if exist "D:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="D:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到Visual Studio 2022 Community (D盘)
    goto :found
)

if exist "D:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="D:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到Visual Studio 2022 Enterprise (D盘)
    goto :found
)

REM 检查D盘 VS2019
if exist "D:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="D:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到Visual Studio 2019 Professional (D盘)
    goto :found
)

if exist "D:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="D:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到Visual Studio 2019 Community (D盘)
    goto :found
)

if exist "D:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="D:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到Visual Studio 2019 Enterprise (D盘)
    goto :found
)

REM 检查C盘的Visual Studio安装
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到Visual Studio 2022 Professional (C盘)
    goto :found
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到Visual Studio 2022 Community (C盘)
    goto :found
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到Visual Studio 2019 Professional (C盘)
    goto :found
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    echo 找到Visual Studio 2019 Community (C盘)
    goto :found
)

REM 使用.NET Framework的MSBuild作为后备
if exist "C:\Windows\Microsoft.NET\Framework64\v4.0.30319\MSBuild.exe" (
    set MSBUILD_PATH="C:\Windows\Microsoft.NET\Framework64\v4.0.30319\MSBuild.exe"
    echo 使用.NET Framework MSBuild
    goto :found
)

REM 如果都找不到，报错退出
echo 错误：找不到MSBuild.exe
echo 请确保已安装Visual Studio 2019/2022或.NET Framework SDK
echo.
echo 已检查的路径：
echo - D:\Program Files\Microsoft Visual Studio\2022\
echo - D:\Program Files (x86)\Microsoft Visual Studio\2019\
echo - C:\Program Files\Microsoft Visual Studio\2022\
echo - C:\Program Files (x86)\Microsoft Visual Studio\2019\
echo - C:\Windows\Microsoft.NET\Framework64\v4.0.30319\
pause
exit /b 1

:found
echo 使用MSBuild路径: %MSBUILD_PATH%
echo.

REM 清理之前的构建
echo 清理之前的构建...
if exist "ShippingNotePrinter\bin" rmdir /s /q "ShippingNotePrinter\bin"
if exist "ShippingNotePrinter\obj" rmdir /s /q "ShippingNotePrinter\obj"

REM 构建Release版本
echo 构建Release版本...
%MSBUILD_PATH% ShippingNotePrinter.sln /p:Configuration=Release /p:Platform="Any CPU" /verbosity:minimal

if %ERRORLEVEL% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo 构建成功！

REM 创建发布目录
if not exist "Release" mkdir "Release"

REM 复制文件到发布目录
echo 复制文件到发布目录...
copy "ShippingNotePrinter\bin\Release\*.*" "Release\"
copy "README.md" "Release\"

echo 发布完成！文件位于 Release 目录中。

REM 显示发布信息
echo.
echo ========================================
echo 发运单打印系统构建完成
echo ========================================
echo 发布目录: Release\
echo 主程序: ShippingNotePrinter.exe
echo 配置文件: ShippingNotePrinter.exe.config
echo ========================================
echo.
echo 部署说明：
echo 1. 将Release目录中的所有文件复制到目标机器
echo 2. 修改配置文件中的服务地址和数据库连接
echo 3. 确保目标机器安装了.NET Framework 4.8
echo 4. 配置针式打印机驱动程序
echo ========================================

pause
