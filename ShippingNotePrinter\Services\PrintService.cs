using System;
using System.Configuration;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Windows.Forms;
using ShippingNotePrinter.Models;
using ZXing;
using ZXing.Common;
using BarcodeWriter = ZXing.Windows.Compatibility.BarcodeWriter;

namespace ShippingNotePrinter.Services
{
    /// <summary>
    /// 发运单打印服务
    /// </summary>
    public class PrintService
    {
        private readonly PrintDocument _printDocument;
        private ShippingNote _currentShippingNote;
        private int _currentPage;
        private readonly Font _headerFont;
        private readonly Font _contentFont;
        private readonly Font _smallFont;
        private readonly Font _boldFont;

        public PrintService()
        {
            _printDocument = new PrintDocument();
            _printDocument.PrintPage += PrintDocument_PrintPage;
            
            // 初始化字体
            _headerFont = new Font("宋体", 16, FontStyle.Bold);
            _contentFont = new Font("宋体", 9);
            _smallFont = new Font("宋体", 8);
            _boldFont = new Font("宋体", 9, FontStyle.Bold);

            // 设置默认打印机
            string defaultPrinter = ConfigurationManager.AppSettings["DefaultPrinterName"];
            if (!string.IsNullOrEmpty(defaultPrinter))
            {
                _printDocument.PrinterSettings.PrinterName = defaultPrinter;
            }

            // 设置纸张大小为A4
            _printDocument.DefaultPageSettings.PaperSize = new PaperSize("A4", 827, 1169);
            _printDocument.DefaultPageSettings.Margins = new Margins(50, 50, 50, 50);
        }

        /// <summary>
        /// 打印发运单
        /// </summary>
        /// <param name="shippingNote">发运单数据</param>
        public void PrintShippingNote(ShippingNote shippingNote)
        {
            try
            {
                _currentShippingNote = shippingNote;
                _currentPage = 1;

                // 显示打印对话框
                PrintDialog printDialog = new PrintDialog();
                printDialog.Document = _printDocument;

                if (printDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    _printDocument.Print();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"打印发运单失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 显示打印预览
        /// </summary>
        /// <param name="shippingNote">发运单数据</param>
        public void ShowPrintPreview(ShippingNote shippingNote)
        {
            try
            {
                _currentShippingNote = shippingNote;
                _currentPage = 1;

                // 显示打印预览对话框
                PrintPreviewDialog previewDialog = new PrintPreviewDialog();
                previewDialog.Document = _printDocument;
                previewDialog.Width = 800;
                previewDialog.Height = 600;
                previewDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                throw new Exception($"显示打印预览失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 直接打印发运单（不显示对话框）
        /// </summary>
        /// <param name="shippingNote">发运单数据</param>
        public void PrintShippingNoteDirect(ShippingNote shippingNote)
        {
            try
            {
                _currentShippingNote = shippingNote;
                _currentPage = 1;
                _printDocument.Print();
            }
            catch (Exception ex)
            {
                throw new Exception($"直接打印发运单失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 打印页面事件处理
        /// </summary>
        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            if (_currentShippingNote == null) return;

            Graphics g = e.Graphics;
            float yPos = e.MarginBounds.Top;
            float leftMargin = e.MarginBounds.Left;
            float rightMargin = e.MarginBounds.Right;
            float pageWidth = e.MarginBounds.Width;

            // 绘制条形码区域
            DrawBarcode(g, leftMargin, yPos, pageWidth);
            yPos += 40;

            // 绘制标题
            DrawTitle(g, leftMargin, yPos, pageWidth);
            yPos += 50;

            // 绘制基本信息
            yPos = DrawBasicInfo(g, leftMargin, yPos, pageWidth);
            yPos += 20;

            // 绘制明细表格
            yPos = DrawItemsTable(g, leftMargin, yPos, pageWidth);
            yPos += 30;

            // 绘制底部信息
            DrawFooterInfo(g, leftMargin, yPos, pageWidth);

            // 检查是否需要更多页面
            e.HasMorePages = false;
        }

        /// <summary>
        /// 绘制条形码和二维码
        /// </summary>
        private void DrawBarcode(Graphics g, float x, float y, float width)
        {
            // 使用订单号作为条形码和二维码内容
            string orderNumber = _currentShippingNote.OrderNumber ?? "SON01250050293";

            // 绘制Code128条形码
            DrawZXingBarcode(g, x, y, 200, 30, orderNumber);

            // 绘制条形码下方的文字
            g.DrawString(orderNumber, _smallFont, Brushes.Black, x, y + 32);

            // 右侧区域布局调整 - 为文字预留足够空间
            float rightAreaWidth = 250; // 右侧区域总宽度
            float rightAreaX = x + width - rightAreaWidth;

            // 绘制二维码
            float qrSize = 80;
            float qrX = rightAreaX;
            float qrY = y;
            DrawZXingQRCode(g, qrX, qrY, qrSize, qrSize, orderNumber);

            // 在二维码右侧绘制文字信息，确保不重叠
            float textX = qrX + qrSize + 10; // 二维码右侧10像素间距
            g.DrawString("第1页，共5页", _smallFont, Brushes.Black, textX, qrY);
            g.DrawString($"NO: {orderNumber}", _smallFont, Brushes.Black, textX, qrY + 15);
            g.DrawString($"打单号: {_currentShippingNote.PrintNumber ?? orderNumber}", _smallFont, Brushes.Black, textX, qrY + 30);
        }

        /// <summary>
        /// 使用ZXing生成Code128条形码
        /// </summary>
        private void DrawZXingBarcode(Graphics g, float x, float y, float width, float height, string content)
        {
            try
            {
                var writer = new BarcodeWriter
                {
                    Format = BarcodeFormat.CODE_128,
                    Options = new EncodingOptions
                    {
                        Width = (int)width,
                        Height = (int)height,
                        Margin = 0
                    }
                };

                using (var bitmap = writer.Write(content))
                {
                    g.DrawImage(bitmap, x, y, width, height);
                }
            }
            catch (Exception ex)
            {
                // 如果生成失败，绘制错误信息
                g.DrawString($"条形码生成失败: {ex.Message}", _smallFont, Brushes.Red, x, y);
            }
        }

        /// <summary>
        /// 使用ZXing生成QR二维码
        /// </summary>
        private void DrawZXingQRCode(Graphics g, float x, float y, float width, float height, string content)
        {
            try
            {
                var writer = new BarcodeWriter
                {
                    Format = BarcodeFormat.QR_CODE,
                    Options = new EncodingOptions
                    {
                        Width = (int)width,
                        Height = (int)height,
                        Margin = 1
                    }
                };

                using (var bitmap = writer.Write(content))
                {
                    g.DrawImage(bitmap, x, y, width, height);
                }
            }
            catch (Exception ex)
            {
                // 如果生成失败，绘制错误信息
                g.DrawString($"二维码生成失败: {ex.Message}", _smallFont, Brushes.Red, x, y);
            }
        }

        // 已移除自定义条形码生成方法，现在使用ZXing.Net库

        // 已移除自定义二维码生成方法，现在使用ZXing.Net库

        // 已移除所有自定义二维码辅助方法，现在使用ZXing.Net库

        // 已移除自定义矩阵生成方法，现在使用ZXing.Net库

        /// <summary>
        /// 计算行高度（考虑文字换行）
        /// </summary>
        private float CalculateRowHeight(Graphics g, string[] values, float[] columnWidths, float[] columnX, Font font)
        {
            float maxHeight = 20; // 最小行高

            for (int i = 0; i < values.Length && i < columnWidths.Length; i++)
            {
                if (string.IsNullOrEmpty(values[i])) continue;

                float columnWidth = columnWidths[i] - 4; // 减去左右边距
                SizeF textSize = g.MeasureString(values[i], font, (int)columnWidth);

                if (textSize.Height > maxHeight)
                {
                    maxHeight = textSize.Height + 4; // 加上上下边距
                }
            }

            return maxHeight;
        }

        /// <summary>
        /// 在单元格中绘制文字（支持自动换行）
        /// </summary>
        private void DrawTextInCell(Graphics g, string text, Rectangle cellRect, Font font, Brush brush)
        {
            if (string.IsNullOrEmpty(text)) return;

            // 设置文字格式
            StringFormat format = new StringFormat
            {
                Alignment = StringAlignment.Near,
                LineAlignment = StringAlignment.Near,
                Trimming = StringTrimming.Word,
                FormatFlags = StringFormatFlags.LineLimit
            };

            // 在单元格内绘制文字，自动换行
            Rectangle textRect = new Rectangle(
                cellRect.X + 2,
                cellRect.Y + 2,
                cellRect.Width - 4,
                cellRect.Height - 4
            );

            g.DrawString(text, font, brush, textRect, format);

            format.Dispose();
        }

        /// <summary>
        /// 绘制二维码定位标记
        /// </summary>
        private void DrawQRPositionMarker(Graphics g, float x, float y, float size)
        {
            // 外框
            g.FillRectangle(Brushes.Black, x, y, size, size);
            // 内部白色
            g.FillRectangle(Brushes.White, x + size/4, y + size/4, size/2, size/2);
            // 中心黑点
            g.FillRectangle(Brushes.Black, x + size*3/8, y + size*3/8, size/4, size/4);
        }

        /// <summary>
        /// 绘制标题
        /// </summary>
        private void DrawTitle(Graphics g, float x, float y, float width)
        {
            string title = "发货通知单";
            SizeF titleSize = g.MeasureString(title, _headerFont);
            float titleX = x + (width - titleSize.Width) / 2;
            g.DrawString(title, _headerFont, Brushes.Black, titleX, y);

            // 右上角信息已经在DrawBarcode方法中绘制，这里不再重复
        }

        /// <summary>
        /// 绘制基本信息
        /// </summary>
        private float DrawBasicInfo(Graphics g, float x, float y, float width)
        {
            float currentY = y;
            float lineHeight = 18;

            // 第一行
            g.DrawString($"客户单位: {_currentShippingNote.CustomerUnit}", _contentFont, Brushes.Black, x, currentY);
            g.DrawString($"发货仓库: {_currentShippingNote.ShippingWarehouse}", _contentFont, Brushes.Black, x + 300, currentY);
            g.DrawString($"联系人: {_currentShippingNote.ContactPerson}", _contentFont, Brushes.Black, x + 500, currentY);
            currentY += lineHeight;

            g.DrawString($"日期: {_currentShippingNote.ShippingDate:yyyy-MM-dd}", _contentFont, Brushes.Black, x + 300, currentY);
            g.DrawString($"电话: {_currentShippingNote.Phone}", _contentFont, Brushes.Black, x + 500, currentY);
            currentY += lineHeight;

            // 第二行
            g.DrawString($"市场区域: {_currentShippingNote.MarketArea}", _contentFont, Brushes.Black, x, currentY);
            g.DrawString($"交货方式: {_currentShippingNote.DeliveryMethod}", _contentFont, Brushes.Black, x + 300, currentY);
            currentY += lineHeight;

            // 第三行
            g.DrawString($"收货地址: {_currentShippingNote.DeliveryAddress}", _contentFont, Brushes.Black, x, currentY);
            currentY += lineHeight;

            // 备注说明
            if (!string.IsNullOrEmpty(_currentShippingNote.Remarks))
            {
                g.DrawString($"备注说明: {_currentShippingNote.Remarks}", _contentFont, Brushes.Black, x, currentY);
                currentY += lineHeight;
            }

            return currentY;
        }

        /// <summary>
        /// 绘制明细表格
        /// </summary>
        private float DrawItemsTable(Graphics g, float x, float y, float width)
        {
            float currentY = y;
            float rowHeight = 20;
            
            // 表格列宽定义
            float[] columnWidths = { 30, 200, 40, 50, 60, 80, 60, 60 };
            float[] columnX = new float[columnWidths.Length];
            columnX[0] = x;
            for (int i = 1; i < columnWidths.Length; i++)
            {
                columnX[i] = columnX[i - 1] + columnWidths[i - 1];
            }

            // 绘制表头
            string[] headers = { "序号", "品名规格 (mm)", "单位", "数量", "单重", "标长/容器总长(只)数", "总标长", "单价" };
            
            // 表头背景
            Rectangle headerRect = new Rectangle((int)x, (int)currentY, (int)width, (int)rowHeight);
            g.FillRectangle(Brushes.LightGray, headerRect);
            g.DrawRectangle(Pens.Black, headerRect);

            for (int i = 0; i < headers.Length && i < columnX.Length; i++)
            {
                g.DrawString(headers[i], _boldFont, Brushes.Black, columnX[i] + 2, currentY + 2);
                if (i < columnX.Length - 1)
                {
                    g.DrawLine(Pens.Black, columnX[i + 1], currentY, columnX[i + 1], currentY + rowHeight);
                }
            }
            currentY += rowHeight;

            // 绘制数据行
            if (_currentShippingNote.Items != null)
            {
                foreach (var item in _currentShippingNote.Items)
                {
                    string[] values = {
                        item.SequenceNumber.ToString(),
                        item.ProductSpecification,
                        item.Unit,
                        item.Quantity.ToString("F0"),
                        item.UnitWeight.ToString("F3"),
                        item.StandardLength.ToString("F0"),
                        item.TotalStandardLength.ToString("F0"),
                        item.UnitPrice.ToString("F5")
                    };

                    // 计算这一行需要的实际高度（考虑换行）
                    float actualRowHeight = CalculateRowHeight(g, values, columnWidths, columnX, _contentFont);

                    // 绘制行边框
                    Rectangle rowRect = new Rectangle((int)x, (int)currentY, (int)width, (int)actualRowHeight);
                    g.DrawRectangle(Pens.Black, rowRect);

                    // 绘制每列的内容
                    for (int i = 0; i < values.Length && i < columnX.Length; i++)
                    {
                        float columnWidth = (i < columnWidths.Length) ? columnWidths[i] : 60;
                        Rectangle cellRect = new Rectangle((int)columnX[i], (int)currentY, (int)columnWidth, (int)actualRowHeight);

                        // 绘制文字（支持换行）
                        DrawTextInCell(g, values[i], cellRect, _contentFont, Brushes.Black);

                        // 绘制列分隔线
                        if (i < columnX.Length - 1)
                        {
                            g.DrawLine(Pens.Black, columnX[i + 1], currentY, columnX[i + 1], currentY + actualRowHeight);
                        }
                    }
                    currentY += actualRowHeight;
                }
            }

            return currentY;
        }

        /// <summary>
        /// 绘制底部信息
        /// </summary>
        private void DrawFooterInfo(Graphics g, float x, float y, float width)
        {
            float lineHeight = 18;
            float currentY = y;

            // 统计信息
            decimal totalWeight = _currentShippingNote.Items?.Sum(i => i.Quantity * i.UnitWeight) ?? 0;
            decimal totalAmount = _currentShippingNote.Items?.Sum(i => i.Amount) ?? 0;
            int totalQuantity = (int)(_currentShippingNote.Items?.Sum(i => i.Quantity) ?? 0);

            g.DrawString($"重量总计: {totalWeight:F4}", _contentFont, Brushes.Black, x, currentY);
            g.DrawString($"本页重量小计: {totalWeight:F3}", _contentFont, Brushes.Black, x + 200, currentY);
            g.DrawString($"本页数量小计: {totalQuantity}", _contentFont, Brushes.Black, x + 400, currentY);
            currentY += lineHeight * 2;

            // 签名区域
            g.DrawString("装货单号:", _contentFont, Brushes.Black, x, currentY);
            g.DrawString("车长:", _contentFont, Brushes.Black, x + 150, currentY);
            g.DrawString("手工米数:", _contentFont, Brushes.Black, x + 250, currentY);
            g.DrawString("货物重量:", _contentFont, Brushes.Black, x + 350, currentY);
            currentY += lineHeight;

            g.DrawString($"业务员: {_currentShippingNote.Salesperson}", _contentFont, Brushes.Black, x, currentY);
            g.DrawString($"主管: {_currentShippingNote.Supervisor}", _contentFont, Brushes.Black, x + 150, currentY);
            g.DrawString($"制单: {_currentShippingNote.Creator}", _contentFont, Brushes.Black, x + 250, currentY);
            g.DrawString($"审核: {_currentShippingNote.Reviewer}", _contentFont, Brushes.Black, x + 350, currentY);
            currentY += lineHeight;

            g.DrawString($"最后打印日期: {DateTime.Now:MM月dd日HH:mm}", _contentFont, Brushes.Black, x, currentY);
            g.DrawString($"是否加急: {(_currentShippingNote.IsUrgent ? "是" : "否")}", _contentFont, Brushes.Black, x + 200, currentY);
            g.DrawString("Q300009", _contentFont, Brushes.Black, x + 350, currentY);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _printDocument?.Dispose();
            _headerFont?.Dispose();
            _contentFont?.Dispose();
            _smallFont?.Dispose();
            _boldFont?.Dispose();
        }
    }
}
