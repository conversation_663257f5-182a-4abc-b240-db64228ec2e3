@echo off
echo ========================================
echo 发运单打印系统环境检查
echo ========================================
echo.

REM 检查.NET Framework
echo 检查.NET Framework...
if exist "C:\Windows\Microsoft.NET\Framework64\v4.0.30319" (
    echo [✓] .NET Framework 4.x 已安装
) else (
    echo [✗] .NET Framework 4.x 未找到
)

if exist "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8" (
    echo [✓] .NET Framework 4.8 开发包已安装
) else (
    echo [✗] .NET Framework 4.8 开发包未找到
)

echo.

REM 检查Visual Studio安装
echo 检查Visual Studio安装...

set VS_FOUND=0

REM 检查D盘
if exist "D:\Program Files\Microsoft Visual Studio\2022" (
    echo [✓] Visual Studio 2022 已安装 (D盘)
    set VS_FOUND=1
)

if exist "D:\Program Files (x86)\Microsoft Visual Studio\2019" (
    echo [✓] Visual Studio 2019 已安装 (D盘)
    set VS_FOUND=1
)

REM 检查C盘
if exist "C:\Program Files\Microsoft Visual Studio\2022" (
    echo [✓] Visual Studio 2022 已安装 (C盘)
    set VS_FOUND=1
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019" (
    echo [✓] Visual Studio 2019 已安装 (C盘)
    set VS_FOUND=1
)

if %VS_FOUND%==0 (
    echo [✗] 未找到Visual Studio安装
)

echo.

REM 检查MSBuild
echo 检查MSBuild...

set MSBUILD_FOUND=0

if exist "D:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo [✓] MSBuild 2022 Professional (D盘)
    set MSBUILD_FOUND=1
)

if exist "D:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo [✓] MSBuild 2022 Community (D盘)
    set MSBUILD_FOUND=1
)

if exist "D:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo [✓] MSBuild 2019 Professional (D盘)
    set MSBUILD_FOUND=1
)

if exist "D:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo [✓] MSBuild 2019 Community (D盘)
    set MSBUILD_FOUND=1
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo [✓] MSBuild 2022 Professional (C盘)
    set MSBUILD_FOUND=1
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo [✓] MSBuild 2019 Professional (C盘)
    set MSBUILD_FOUND=1
)

if exist "C:\Windows\Microsoft.NET\Framework64\v4.0.30319\MSBuild.exe" (
    echo [✓] .NET Framework MSBuild
    set MSBUILD_FOUND=1
)

if %MSBUILD_FOUND%==0 (
    echo [✗] 未找到MSBuild
)

echo.

REM 检查项目文件
echo 检查项目文件...

if exist "ShippingNotePrinter.sln" (
    echo [✓] 解决方案文件存在
) else (
    echo [✗] 解决方案文件不存在
)

if exist "ShippingNotePrinter\ShippingNotePrinter.csproj" (
    echo [✓] 项目文件存在
) else (
    echo [✗] 项目文件不存在
)

if exist "ShippingNotePrinter\App.config" (
    echo [✓] 配置文件存在
) else (
    echo [✗] 配置文件不存在
)

echo.

REM 总结
echo ========================================
echo 环境检查完成
echo ========================================

if %VS_FOUND%==1 if %MSBUILD_FOUND%==1 (
    echo [✓] 环境配置正常，可以构建项目
    echo.
    echo 建议使用以下脚本构建：
    echo - build-auto.bat (自动检测Visual Studio)
    echo - build.bat (手动指定路径)
) else (
    echo [✗] 环境配置有问题，请检查Visual Studio安装
    echo.
    echo 解决方案：
    echo 1. 安装Visual Studio 2019或2022
    echo 2. 确保安装了.NET Framework 4.8开发包
    echo 3. 或者安装.NET Framework 4.8 Developer Pack
)

echo.
pause
