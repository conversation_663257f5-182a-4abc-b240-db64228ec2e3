using System;
using System.Configuration;
using System.IO;
using System.Net;
using System.Text;

namespace ShippingNotePrinter.Services
{
    /// <summary>
    /// 短信验证码服务
    /// </summary>
    public class SmsService
    {
        private readonly string _smsServiceUrl;
        private readonly int _timeout;

        public SmsService()
        {
            _smsServiceUrl = ConfigurationManager.AppSettings["SmsServiceUrl"] ?? "http://localhost:8080/axis2/services/SmsService";
            _timeout = 30000; // 30秒超时
        }

        /// <summary>
        /// 发送短信验证码
        /// </summary>
        /// <param name="phoneNumber">手机号</param>
        /// <returns>是否发送成功</returns>
        public bool SendVerificationCode(string phoneNumber)
        {
            try
            {
                // 调试模式：模拟发送成功
                System.Threading.Thread.Sleep(1000); // 模拟网络延迟
                return true;

                // 实际部署时启用以下代码
                /*
                string soapEnvelope = CreateSendSmsCodeSoapEnvelope(phoneNumber);
                string response = SendSoapRequest(soapEnvelope, "sendVerificationCode");
                return ParseSendSmsResponse(response);
                */
            }
            catch (Exception ex)
            {
                throw new Exception($"发送验证码失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证短信验证码
        /// </summary>
        /// <param name="phoneNumber">手机号</param>
        /// <param name="verificationCode">验证码</param>
        /// <returns>验证是否成功</returns>
        public bool VerifyCode(string phoneNumber, string verificationCode)
        {
            try
            {
                // 调试模式：简单验证码验证（任何6位数字都通过）
                System.Threading.Thread.Sleep(500); // 模拟网络延迟
                return !string.IsNullOrEmpty(verificationCode) && verificationCode.Length >= 4;

                // 实际部署时启用以下代码
                /*
                string soapEnvelope = CreateVerifyCodeSoapEnvelope(phoneNumber, verificationCode);
                string response = SendSoapRequest(soapEnvelope, "verifyCode");
                return ParseVerifyCodeResponse(response);
                */
            }
            catch (Exception ex)
            {
                throw new Exception($"验证码验证失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建发送验证码的SOAP信封
        /// </summary>
        private string CreateSendSmsCodeSoapEnvelope(string phoneNumber)
        {
            return $@"<?xml version=""1.0"" encoding=""utf-8""?>
<soap:Envelope xmlns:soap=""http://schemas.xmlsoap.org/soap/envelope/"" 
               xmlns:sms=""http://service.sms.com"">
    <soap:Header/>
    <soap:Body>
        <sms:sendVerificationCode>
            <sms:phoneNumber>{phoneNumber}</sms:phoneNumber>
        </sms:sendVerificationCode>
    </soap:Body>
</soap:Envelope>";
        }

        /// <summary>
        /// 创建验证验证码的SOAP信封
        /// </summary>
        private string CreateVerifyCodeSoapEnvelope(string phoneNumber, string verificationCode)
        {
            return $@"<?xml version=""1.0"" encoding=""utf-8""?>
<soap:Envelope xmlns:soap=""http://schemas.xmlsoap.org/soap/envelope/"" 
               xmlns:sms=""http://service.sms.com"">
    <soap:Header/>
    <soap:Body>
        <sms:verifyCode>
            <sms:phoneNumber>{phoneNumber}</sms:phoneNumber>
            <sms:verificationCode>{verificationCode}</sms:verificationCode>
        </sms:verifyCode>
    </soap:Body>
</soap:Envelope>";
        }

        /// <summary>
        /// 发送SOAP请求
        /// </summary>
        private string SendSoapRequest(string soapEnvelope, string soapAction)
        {
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(_smsServiceUrl);
            request.Method = "POST";
            request.ContentType = "text/xml; charset=utf-8";
            request.Headers.Add("SOAPAction", $"\"{soapAction}\"");
            request.Timeout = _timeout;

            // 写入请求数据
            byte[] data = Encoding.UTF8.GetBytes(soapEnvelope);
            request.ContentLength = data.Length;

            using (Stream requestStream = request.GetRequestStream())
            {
                requestStream.Write(data, 0, data.Length);
            }

            // 获取响应
            using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
            using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
            {
                return reader.ReadToEnd();
            }
        }

        /// <summary>
        /// 解析发送短信响应
        /// </summary>
        private bool ParseSendSmsResponse(string xmlResponse)
        {
            try
            {
                // 简单的响应解析，实际需要根据服务端返回格式调整
                return xmlResponse.Contains("success") || xmlResponse.Contains("true");
            }
            catch (Exception ex)
            {
                throw new Exception($"解析发送短信响应失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 解析验证码验证响应
        /// </summary>
        private bool ParseVerifyCodeResponse(string xmlResponse)
        {
            try
            {
                // 简单的响应解析，实际需要根据服务端返回格式调整
                return xmlResponse.Contains("success") || xmlResponse.Contains("true");
            }
            catch (Exception ex)
            {
                throw new Exception($"解析验证码验证响应失败: {ex.Message}", ex);
            }
        }
    }
}
