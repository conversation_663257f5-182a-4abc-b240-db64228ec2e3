using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace ShippingNotePrinter.Services
{
    /// <summary>
    /// 现代化样式管理器
    /// </summary>
    public static class ModernStyleManager
    {
        #region 颜色主题

        public static class Colors
        {
            // 主色调
            public static readonly Color Primary = Color.FromArgb(52, 152, 219);
            public static readonly Color PrimaryDark = Color.FromArgb(41, 128, 185);
            public static readonly Color PrimaryLight = Color.FromArgb(174, 214, 241);

            // 成功色
            public static readonly Color Success = Color.FromArgb(46, 204, 113);
            public static readonly Color SuccessDark = Color.FromArgb(39, 174, 96);

            // 警告色
            public static readonly Color Warning = Color.FromArgb(243, 156, 18);
            public static readonly Color WarningDark = Color.FromArgb(211, 84, 0);

            // 错误色
            public static readonly Color Error = Color.FromArgb(231, 76, 60);
            public static readonly Color ErrorDark = Color.FromArgb(192, 57, 43);

            // 中性色
            public static readonly Color Secondary = Color.FromArgb(149, 165, 166);
            public static readonly Color SecondaryDark = Color.FromArgb(127, 140, 141);

            // 背景色
            public static readonly Color Background = Color.FromArgb(248, 249, 250);
            public static readonly Color CardBackground = Color.White;
            public static readonly Color BorderLight = Color.FromArgb(233, 236, 239);

            // 文字色
            public static readonly Color TextPrimary = Color.FromArgb(52, 58, 64);
            public static readonly Color TextSecondary = Color.FromArgb(108, 117, 125);
            public static readonly Color TextMuted = Color.FromArgb(134, 142, 150);

            // 阴影色
            public static readonly Color Shadow = Color.FromArgb(30, 0, 0, 0);
            public static readonly Color ShadowLight = Color.FromArgb(15, 0, 0, 0);
        }

        #endregion

        #region 尺寸规范

        public static class Sizes
        {
            // 圆角半径
            public const int BorderRadiusSmall = 4;
            public const int BorderRadiusMedium = 8;
            public const int BorderRadiusLarge = 12;

            // 间距
            public const int SpacingXSmall = 4;
            public const int SpacingSmall = 8;
            public const int SpacingMedium = 16;
            public const int SpacingLarge = 24;
            public const int SpacingXLarge = 32;

            // 按钮尺寸
            public static readonly Size ButtonSmall = new Size(100, 32);
            public static readonly Size ButtonMedium = new Size(120, 40);
            public static readonly Size ButtonLarge = new Size(180, 48);
            public static readonly Size ButtonXLarge = new Size(240, 60);

            // 卡片尺寸
            public static readonly Size CardSmall = new Size(200, 150);
            public static readonly Size CardMedium = new Size(280, 200);
            public static readonly Size CardLarge = new Size(350, 250);
        }

        #endregion

        #region 样式应用方法

        /// <summary>
        /// 应用现代化按钮样式
        /// </summary>
        public static void ApplyModernButtonStyle(Button button, ButtonStyle style = ButtonStyle.Primary)
        {
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Cursor = Cursors.Hand;
            
            // 根据按钮样式设置合适的字体大小
            Font buttonFont;
            switch (style)
            {
                case ButtonStyle.Primary:
                case ButtonStyle.Success:
                    // 主要操作按钮使用中等字体，更好地适应按钮高度
                    buttonFont = new Font("微软雅黑", 16F, FontStyle.Bold);
                    break;
                case ButtonStyle.Secondary:
                    // 次要按钮使用中等字体
                    buttonFont = new Font("微软雅黑", 16F, FontStyle.Bold);
                    break;
                case ButtonStyle.Outline:
                    // 数字按键使用中等字体
                    buttonFont = new Font("微软雅黑", 18F, FontStyle.Bold);
                    break;
                case ButtonStyle.Warning:
                case ButtonStyle.Error:
                    // 特殊按钮使用中等字体
                    buttonFont = new Font("微软雅黑", 16F, FontStyle.Bold);
                    break;
                default:
                    buttonFont = new Font("微软雅黑", 20F, FontStyle.Bold);
                    break;
            }
            button.Font = buttonFont;

            // 设置颜色主题
            switch (style)
            {
                case ButtonStyle.Primary:
                    button.BackColor = Colors.Primary;
                    button.ForeColor = Color.White;
                    AddButtonHoverEffect(button, Colors.PrimaryDark);
                    break;
                case ButtonStyle.Success:
                    button.BackColor = Colors.Success;
                    button.ForeColor = Color.White;
                    AddButtonHoverEffect(button, Colors.SuccessDark);
                    break;
                case ButtonStyle.Warning:
                    button.BackColor = Colors.Warning;
                    button.ForeColor = Color.White;
                    AddButtonHoverEffect(button, Colors.WarningDark);
                    break;
                case ButtonStyle.Error:
                    button.BackColor = Colors.Error;
                    button.ForeColor = Color.White;
                    AddButtonHoverEffect(button, Colors.ErrorDark);
                    break;
                case ButtonStyle.Secondary:
                    button.BackColor = Colors.Secondary;
                    button.ForeColor = Color.White;
                    AddButtonHoverEffect(button, Colors.SecondaryDark);
                    break;
                case ButtonStyle.Outline:
                    button.BackColor = Color.Transparent;
                    button.ForeColor = Colors.Primary;
                    button.FlatAppearance.BorderColor = Colors.Primary;
                    button.FlatAppearance.BorderSize = 2;
                    AddButtonHoverEffect(button, Colors.PrimaryLight, Colors.Primary);
                    break;
            }


        }

        /// <summary>
        /// 应用现代化卡片样式
        /// </summary>
        public static void ApplyModernCardStyle(Panel panel, bool addShadow = true)
        {
            panel.BackColor = Colors.CardBackground;
            
            // 添加圆角
            panel.Paint += (s, e) =>
            {
                var pnl = s as Panel;
                using (var path = GetRoundedRectanglePath(pnl.ClientRectangle, Sizes.BorderRadiusLarge))
                {
                    pnl.Region = new Region(path);
                }

                if (addShadow)
                {
                    // 绘制阴影
                    using (var shadowBrush = new SolidBrush(Colors.Shadow))
                    {
                        var shadowRect = new Rectangle(4, 4, pnl.Width, pnl.Height);
                        using (var shadowPath = GetRoundedRectanglePath(shadowRect, Sizes.BorderRadiusLarge))
                        {
                            e.Graphics.FillPath(shadowBrush, shadowPath);
                        }
                    }

                    // 绘制主背景
                    using (var backgroundBrush = new SolidBrush(Colors.CardBackground))
                    using (var path = GetRoundedRectanglePath(pnl.ClientRectangle, Sizes.BorderRadiusLarge))
                    {
                        e.Graphics.FillPath(backgroundBrush, path);
                    }
                }
            };


        }

        /// <summary>
        /// 应用现代化文本框样式
        /// </summary>
        public static void ApplyModernTextBoxStyle(TextBox textBox)
        {
            textBox.BorderStyle = BorderStyle.None;
            textBox.Font = new Font("微软雅黑", 12F);
            textBox.BackColor = Color.White;
            textBox.ForeColor = Colors.TextPrimary;

            // 创建包装面板来添加边框和圆角
            var wrapper = new Panel
            {
                Size = new Size(textBox.Width + 20, textBox.Height + 20),
                Location = new Point(textBox.Location.X - 10, textBox.Location.Y - 10),
                BackColor = Color.White
            };

            textBox.Location = new Point(10, 10);
            textBox.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;

            wrapper.Controls.Add(textBox);
            textBox.Parent.Controls.Add(wrapper);
            textBox.Parent.Controls.Remove(textBox);

            // 添加边框和圆角
            wrapper.Paint += (s, e) =>
            {
                var pnl = s as Panel;
                using (var path = GetRoundedRectanglePath(pnl.ClientRectangle, Sizes.BorderRadiusMedium))
                {
                    pnl.Region = new Region(path);
                }

                // 绘制边框
                using (var pen = new Pen(Colors.BorderLight, 2))
                using (var path = GetRoundedRectanglePath(pnl.ClientRectangle, Sizes.BorderRadiusMedium))
                {
                    e.Graphics.DrawPath(pen, path);
                }
            };

            // 添加焦点效果
            textBox.Enter += (s, e) =>
            {
                wrapper.Invalidate();
            };

            textBox.Leave += (s, e) =>
            {
                wrapper.Invalidate();
            };
        }

        /// <summary>
        /// 应用现代化ListView样式
        /// </summary>
        public static void ApplyModernListViewStyle(ListView listView)
        {
            listView.View = View.Details;
            listView.FullRowSelect = true;
            listView.GridLines = false;
            listView.HeaderStyle = ColumnHeaderStyle.Nonclickable;
            listView.Font = new Font("微软雅黑", 10F);
            listView.BackColor = Colors.CardBackground;
            listView.ForeColor = Colors.TextPrimary;
            listView.BorderStyle = BorderStyle.None;

            // 自定义绘制
            listView.OwnerDraw = true;
            listView.DrawItem += ListView_DrawItem;
            listView.DrawSubItem += ListView_DrawSubItem;
            listView.DrawColumnHeader += ListView_DrawColumnHeader;
        }

        #endregion

        #region 私有辅助方法

        private static void AddButtonHoverEffect(Button button, Color hoverColor, Color? hoverTextColor = null)
        {
            // 禁用悬停效果 - 不添加任何MouseEnter/MouseLeave事件
            // 按钮保持原始背景色不变
        }

        private static void AddButtonRoundedCorners(Button button, int radius)
        {
            button.Paint += (s, e) =>
            {
                var btn = s as Button;
                using (var path = GetRoundedRectanglePath(btn.ClientRectangle, radius))
                {
                    btn.Region = new Region(path);
                }
            };
        }

        private static void AddCardHoverEffect(Panel panel)
        {
            var originalLocation = panel.Location;

            panel.MouseEnter += (s, e) =>
            {
                panel.Location = new Point(originalLocation.X, originalLocation.Y - 2);
            };

            panel.MouseLeave += (s, e) =>
            {
                panel.Location = originalLocation;
            };
        }

        private static GraphicsPath GetRoundedRectanglePath(Rectangle rect, int radius)
        {
            var path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }

        private static void ListView_DrawItem(object sender, DrawListViewItemEventArgs e)
        {
            if (e.Item.Selected)
            {
                using (var brush = new SolidBrush(Colors.PrimaryLight))
                {
                    e.Graphics.FillRectangle(brush, e.Bounds);
                }
            }
            else if (e.ItemIndex % 2 == 1)
            {
                using (var brush = new SolidBrush(Color.FromArgb(248, 249, 250)))
                {
                    e.Graphics.FillRectangle(brush, e.Bounds);
                }
            }
            else
            {
                e.DrawDefault = true;
            }
        }

        private static void ListView_DrawSubItem(object sender, DrawListViewSubItemEventArgs e)
        {
            if (e.Item.Selected)
            {
                using (var brush = new SolidBrush(Colors.Primary))
                {
                    e.Graphics.DrawString(e.SubItem.Text, e.Item.ListView.Font, brush, e.Bounds.Location);
                }
            }
            else
            {
                using (var brush = new SolidBrush(Colors.TextPrimary))
                {
                    e.Graphics.DrawString(e.SubItem.Text, e.Item.ListView.Font, brush, e.Bounds.Location);
                }
            }
        }

        private static void ListView_DrawColumnHeader(object sender, DrawListViewColumnHeaderEventArgs e)
        {
            using (var brush = new SolidBrush(Colors.Background))
            {
                e.Graphics.FillRectangle(brush, e.Bounds);
            }

            using (var pen = new Pen(Colors.BorderLight))
            {
                e.Graphics.DrawLine(pen, e.Bounds.Left, e.Bounds.Bottom - 1, e.Bounds.Right, e.Bounds.Bottom - 1);
            }

            using (var brush = new SolidBrush(Colors.TextSecondary))
            using (var font = new Font("微软雅黑", 10F, FontStyle.Bold))
            {
                var textRect = new Rectangle(e.Bounds.X + 8, e.Bounds.Y, e.Bounds.Width - 8, e.Bounds.Height);
                e.Graphics.DrawString(e.Header.Text, font, brush, textRect, 
                    new StringFormat { LineAlignment = StringAlignment.Center });
            }
        }

        #endregion

        #region 枚举定义

        public enum ButtonStyle
        {
            Primary,
            Success,
            Warning,
            Error,
            Secondary,
            Outline
        }

        #endregion
    }
} 