using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace ShippingNotePrinter.Forms
{
    /// <summary>
    /// 现代化自定义消息框
    /// </summary>
    public partial class ModernMessageBox : Form
    {
        private Label lblTitle;
        private Label lblMessage;
        private Panel pnlIcon;
        private Panel pnlButtons;
        private Button btnPrimary;
        private Button btnSecondary;
        private Timer fadeTimer;
        private bool isClosing = false;

        public DialogResult Result { get; private set; } = DialogResult.None;
        private MessageBoxButtons _currentButtons;

        public ModernMessageBox()
        {
            InitializeComponent();
            SetupModernStyle();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 窗体设置
            this.Text = "";
            this.Size = new Size(480, 280);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.White;
            this.ShowInTaskbar = false;
            this.TopMost = true;

            // 标题标签
            this.lblTitle = new Label
            {
                Font = new Font("微软雅黑", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(80, 30),
                Size = new Size(360, 30),
                Text = "提示"
            };

            // 消息标签
            this.lblMessage = new Label
            {
                Font = new Font("微软雅黑", 12F),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(80, 70),
                Size = new Size(360, 80),
                Text = "这是一条消息",
                AutoSize = false,
                TextAlign = ContentAlignment.TopLeft
            };

            // 图标面板
            this.pnlIcon = new Panel
            {
                Location = new Point(30, 30),
                Size = new Size(40, 40),
                BackColor = Color.Transparent
            };

            // 按钮面板
            this.pnlButtons = new Panel
            {
                Location = new Point(0, 200),
                Size = new Size(480, 80),
                BackColor = Color.Transparent
            };

            // 主按钮
            this.btnPrimary = new Button
            {
                Size = new Size(100, 40),
                Location = new Point(270, 20),
                Text = "确定",
                Font = new Font("微软雅黑", 10F),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };
            this.btnPrimary.FlatAppearance.BorderSize = 0;
            this.btnPrimary.Click += BtnPrimary_Click;

            // 次按钮
            this.btnSecondary = new Button
            {
                Size = new Size(100, 40),
                Location = new Point(160, 20),
                Text = "取消",
                Font = new Font("微软雅黑", 10F),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                Cursor = Cursors.Hand,
                Visible = false
            };
            this.btnSecondary.FlatAppearance.BorderSize = 0;
            this.btnSecondary.Click += BtnSecondary_Click;

            // 添加控件
            this.pnlButtons.Controls.Add(this.btnPrimary);
            this.pnlButtons.Controls.Add(this.btnSecondary);
            this.Controls.Add(this.lblTitle);
            this.Controls.Add(this.lblMessage);
            this.Controls.Add(this.pnlIcon);
            this.Controls.Add(this.pnlButtons);

            this.ResumeLayout(false);
        }

        private void SetupModernStyle()
        {
            // 设置窗体圆角
            this.Region = GetRoundedRegion(this.ClientRectangle, 8);

            // 添加阴影效果
            this.Paint += ModernMessageBox_Paint;

            // 设置按钮圆角
            SetButtonRoundedCorners(btnPrimary, 6);
            SetButtonRoundedCorners(btnSecondary, 6);

            // 添加按钮悬停效果
            AddButtonHoverEffect(btnPrimary, Color.FromArgb(41, 128, 185));
            AddButtonHoverEffect(btnSecondary, Color.FromArgb(127, 140, 141));

            // 淡入动画
            SetupFadeAnimation();
        }

        private void ModernMessageBox_Paint(object sender, PaintEventArgs e)
        {
            // 绘制主背景
            using (var bgBrush = new SolidBrush(Color.White))
            {
                e.Graphics.FillRectangle(bgBrush, this.ClientRectangle);
            }

            // 绘制完整的边框 - 使用ClientRectangle确保正确的绘制区域
            using (var borderPen = new Pen(Color.FromArgb(52, 152, 219), 2))
            {
                var borderRect = new Rectangle(1, 1, this.ClientSize.Width - 2, this.ClientSize.Height - 2);
                e.Graphics.DrawRectangle(borderPen, borderRect);
            }

            // 绘制顶部装饰线（加粗）- 在边框内部绘制，避免覆盖
            using (var pen = new Pen(Color.FromArgb(52, 152, 219), 3))
            {
                e.Graphics.DrawLine(pen, 3, 3, this.ClientSize.Width - 3, 3);
            }
        }

        private Region GetRoundedRegion(Rectangle rect, int radius)
        {
            var path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return new Region(path);
        }

        private void SetButtonRoundedCorners(Button button, int radius)
        {
            button.Paint += (s, e) =>
            {
                var btn = s as Button;
                using (var path = GetRoundedRectanglePath(btn.ClientRectangle, radius))
                {
                    btn.Region = new Region(path);
                }
            };
        }

        private GraphicsPath GetRoundedRectanglePath(Rectangle rect, int radius)
        {
            var path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }

        private void AddButtonHoverEffect(Button button, Color hoverColor)
        {
            var originalColor = button.BackColor;
            
            button.MouseEnter += (s, e) =>
            {
                button.BackColor = hoverColor;
            };
            
            button.MouseLeave += (s, e) =>
            {
                button.BackColor = originalColor;
            };
        }

        private void SetupFadeAnimation()
        {
            this.Opacity = 0;
            fadeTimer = new Timer { Interval = 20 };
            fadeTimer.Tick += (s, e) =>
            {
                if (isClosing)
                {
                    this.Opacity -= 0.1;
                    if (this.Opacity <= 0)
                    {
                        fadeTimer.Stop();
                        this.Hide();
                    }
                }
                else
                {
                    this.Opacity += 0.1;
                    if (this.Opacity >= 1)
                    {
                        fadeTimer.Stop();
                    }
                }
            };
            fadeTimer.Start();
        }

        private void BtnPrimary_Click(object sender, EventArgs e)
        {
            // 根据按钮类型设置正确的 DialogResult
            switch (_currentButtons)
            {
                case MessageBoxButtons.YesNo:
                case MessageBoxButtons.YesNoCancel:
                    Result = DialogResult.Yes;
                    break;
                default:
                    Result = DialogResult.OK;
                    break;
            }
            CloseWithAnimation();
        }

        private void BtnSecondary_Click(object sender, EventArgs e)
        {
            // 根据按钮类型设置正确的 DialogResult
            switch (_currentButtons)
            {
                case MessageBoxButtons.YesNo:
                case MessageBoxButtons.YesNoCancel:
                    Result = DialogResult.No;
                    break;
                default:
                    Result = DialogResult.Cancel;
                    break;
            }
            CloseWithAnimation();
        }

        private void CloseWithAnimation()
        {
            isClosing = true;
            fadeTimer.Start();
        }

        // 静态方法用于显示不同类型的消息框
        public static DialogResult Show(string message, string title = "提示", 
            MessageBoxButtons buttons = MessageBoxButtons.OK, 
            MessageBoxIcon icon = MessageBoxIcon.Information)
        {
            var messageBox = new ModernMessageBox();
            messageBox.SetMessage(message, title, buttons, icon);
            messageBox.ShowDialog();
            return messageBox.Result;
        }

        public static DialogResult Show(IWin32Window owner, string message, string title = "提示",
            MessageBoxButtons buttons = MessageBoxButtons.OK,
            MessageBoxIcon icon = MessageBoxIcon.Information)
        {
            var messageBox = new ModernMessageBox();
            messageBox.SetMessage(message, title, buttons, icon);
            messageBox.ShowDialog(owner);
            return messageBox.Result;
        }

        private void SetMessage(string message, string title, MessageBoxButtons buttons, MessageBoxIcon icon)
        {
            lblTitle.Text = title;
            lblMessage.Text = message;

            // 设置图标
            SetIcon(icon);

            // 设置按钮
            SetButtons(buttons);

            // 调整消息框大小
            AdjustSize();
        }

        private void SetIcon(MessageBoxIcon icon)
        {
            pnlIcon.Paint += (s, e) =>
            {
                var iconColor = Color.FromArgb(52, 152, 219); // 默认蓝色
                var iconText = "ℹ"; // 默认信息图标

                switch (icon)
                {
                    case MessageBoxIcon.Information:
                        iconColor = Color.FromArgb(52, 152, 219);
                        iconText = "ℹ";
                        break;
                    case MessageBoxIcon.Warning:
                        iconColor = Color.FromArgb(243, 156, 18);
                        iconText = "⚠";
                        break;
                    case MessageBoxIcon.Error:
                        iconColor = Color.FromArgb(231, 76, 60);
                        iconText = "✖";
                        break;
                    case MessageBoxIcon.Question:
                        iconColor = Color.FromArgb(52, 152, 219);
                        iconText = "?";
                        break;
                }

                // 绘制圆形背景
                using (var brush = new SolidBrush(Color.FromArgb(50, iconColor)))
                {
                    e.Graphics.FillEllipse(brush, new Rectangle(0, 0, 40, 40));
                }

                // 绘制图标文字
                using (var brush = new SolidBrush(iconColor))
                using (var font = new Font("Segoe UI Symbol", 20F, FontStyle.Bold))
                {
                    var textSize = e.Graphics.MeasureString(iconText, font);
                    var x = (40 - textSize.Width) / 2;
                    var y = (40 - textSize.Height) / 2;
                    e.Graphics.DrawString(iconText, font, brush, x, y);
                }
            };
        }

        private void SetButtons(MessageBoxButtons buttons)
        {
            _currentButtons = buttons;
            switch (buttons)
            {
                case MessageBoxButtons.OK:
                    btnPrimary.Text = "确定";
                    btnPrimary.Location = new Point(190, 20);
                    btnSecondary.Visible = false;
                    break;
                case MessageBoxButtons.OKCancel:
                    btnPrimary.Text = "确定";
                    btnSecondary.Text = "取消";
                    btnSecondary.Visible = true;
                    break;
                case MessageBoxButtons.YesNo:
                    btnPrimary.Text = "是";
                    btnSecondary.Text = "否";
                    btnSecondary.Visible = true;
                    break;
                case MessageBoxButtons.YesNoCancel:
                    btnPrimary.Text = "是";
                    btnSecondary.Text = "否";
                    btnSecondary.Visible = true;
                    break;
            }
        }

        private void AdjustSize()
        {
            // 根据消息长度调整高度
            using (var g = this.CreateGraphics())
            {
                var textSize = g.MeasureString(lblMessage.Text, lblMessage.Font, lblMessage.Width);
                var newHeight = Math.Max(280, (int)textSize.Height + 200);
                
                this.Size = new Size(480, newHeight);
                pnlButtons.Location = new Point(0, newHeight - 80);
                
                // 重新设置圆角
                this.Region = GetRoundedRegion(this.ClientRectangle, 8);
            }
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            btnPrimary.Focus();
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            if (keyData == Keys.Escape)
            {
                Result = DialogResult.Cancel;
                CloseWithAnimation();
                return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }
    }
} 