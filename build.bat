@echo off
echo 正在构建发运单打印系统...

REM 设置MSBuild路径
set MSBUILD_PATH="D:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
if not exist %MSBUILD_PATH% (
    set MSBUILD_PATH="D:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
)
if not exist %MSBUILD_PATH% (
    set MSBUILD_PATH="D:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
)
if not exist %MSBUILD_PATH% (
    set MSBUILD_PATH="D:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
)
if not exist %MSBUILD_PATH% (
    set MSBUILD_PATH="D:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
)
if not exist %MSBUILD_PATH% (
    set MSBUILD_PATH="D:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
)
if not exist %MSBUILD_PATH% (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
)
if not exist %MSBUILD_PATH% (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
)
if not exist %MSBUILD_PATH% (
    set MSBUILD_PATH="C:\Windows\Microsoft.NET\Framework64\v4.0.30319\MSBuild.exe"
)

REM 检查MSBuild是否存在
if not exist %MSBUILD_PATH% (
    echo 错误：找不到MSBuild.exe，请确保已安装Visual Studio或.NET Framework SDK
    pause
    exit /b 1
)

echo 使用MSBuild路径: %MSBUILD_PATH%

REM 清理之前的构建
echo 清理之前的构建...
if exist "ShippingNotePrinter\bin" rmdir /s /q "ShippingNotePrinter\bin"
if exist "ShippingNotePrinter\obj" rmdir /s /q "ShippingNotePrinter\obj"

REM 构建Release版本
echo 构建Release版本...
%MSBUILD_PATH% ShippingNotePrinter.sln /p:Configuration=Release /p:Platform="Any CPU" /verbosity:minimal

if %ERRORLEVEL% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo 构建成功！

REM 创建发布目录
if not exist "Release" mkdir "Release"

REM 复制文件到发布目录
echo 复制文件到发布目录...
copy "ShippingNotePrinter\bin\Release\*.*" "Release\"
copy "README.md" "Release\"

echo 发布完成！文件位于 Release 目录中。

REM 显示发布信息
echo.
echo ========================================
echo 发运单打印系统构建完成
echo ========================================
echo 发布目录: Release\
echo 主程序: ShippingNotePrinter.exe
echo 配置文件: ShippingNotePrinter.exe.config
echo ========================================
echo.
echo 部署说明：
echo 1. 将Release目录中的所有文件复制到目标机器
echo 2. 修改配置文件中的服务地址和数据库连接
echo 3. 确保目标机器安装了.NET Framework 4.8
echo 4. 配置针式打印机驱动程序
echo ========================================

pause
