# 发运单打印格式预览

## 🖨️ 打印实现原理

当您点击"打印发运单"按钮时，系统执行以下流程：

1. **MainForm.btnPrint_Click** → 获取选中的发运单
2. **SoapService.GetShippingNoteDetail** → 获取详细数据（调试模式返回测试数据）
3. **PrintService.PrintShippingNoteDirect** → 直接发送到默认打印机
4. **PrintDocument_PrintPage** → 绘制打印内容

## 📄 打印格式布局

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||  │ ← 条形码
│ SON01250050293                                                        [QR]  │ ← 条形码号 + 二维码
├─────────────────────────────────────────────────────────────────────────────┤
│                            发货通知单                                        │ ← 标题 (16pt 粗体)
│                                                          第1页，共5页        │
│                                                    NO: SON01250050293       │
│                                                  打单号: SON01250050293     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 客户单位: 公元管道（重庆）有限公司    发货仓库: 公司总库    联系人: 小严      │
│                                    日期: 2025-08-05      电话: 023-49211551 │
│ 市场区域: 公司总库                  交货方式: 送货                          │
│ 收货地址: 重庆市渝北区龙兴大道2408号公元管道（重庆）有限公司                │
│ 备注说明: 请提货单发货定制来料包装                                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────┬──────────────────────┬────┬────┬────┬──────────┬────┬──────┐        │
│ │序号 │    品名规格 (mm)     │单位│数量│单重│标长/容器总│总标│ 单价 │        │
│ │     │                      │    │    │    │长(只)数  │长  │      │        │
│ ├─────┼──────────────────────┼────┼────┼────┼──────────┼────┼──────┤        │
│ │  1  │115-公元PP-R防冻管件  │ 箱 │ 14 │6260│   6740   │6740│0.689 │        │
│ ├─────┼──────────────────────┼────┼────┼────┼──────────┼────┼──────┤        │
│ │  2  │公元PPR防冻弯头管件   │ 箱 │ 1  │320 │   320    │320 │0.044 │        │
│ │     │（橙色）三通 φ 20     │    │    │    │          │    │      │        │
│ ├─────┼──────────────────────┼────┼────┼────┼──────────┼────┼──────┤        │
│ │  3  │公元PPR防冻弯头管件   │ 箱 │6100│240 │   100    │100 │0.018 │        │
│ │     │（橙色）异径三通φ25*20│    │    │    │          │    │      │        │
│ └─────┴──────────────────────┴────┴────┴────┴──────────┴────┴──────┘        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 重量总计: 1718.4174    本页重量小计: 1718.417    本页数量小计: 6840         │
│                                                                             │
│ 装货单号:              车长:              手工米数:              货物重量:   │
│ 业务员: 王五           主管: 主管          制单: 制单            审核: 防疫   │
│ 最后打印日期: 08月06日08:43              是否加急: 是            Q300009     │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 🎨 字体和样式设置

- **标题字体**: 宋体 16pt 粗体
- **内容字体**: 宋体 9pt 常规
- **小字体**: 宋体 8pt 常规
- **粗体字**: 宋体 9pt 粗体

## 📏 纸张设置

- **纸张大小**: A4 (827 x 1169 点)
- **页边距**: 上下左右各 50 点
- **打印方向**: 纵向

## 🔧 打印功能说明

### 1. 直接打印 (`PrintShippingNoteDirect`)
```csharp
// 直接发送到默认打印机，不显示对话框
_printDocument.Print();
```

### 2. 带对话框打印 (`PrintShippingNote`)
```csharp
// 显示打印对话框，用户可选择打印机
PrintDialog printDialog = new PrintDialog();
printDialog.Document = _printDocument;
if (printDialog.ShowDialog() == DialogResult.OK)
{
    _printDocument.Print();
}
```

### 3. 打印预览 (`ShowPrintPreview`) ⭐ **新增功能**
```csharp
// 显示打印预览窗口
PrintPreviewDialog previewDialog = new PrintPreviewDialog();
previewDialog.Document = _printDocument;
previewDialog.ShowDialog();
```

## 📋 绘制区域详解

### 1. 条形码区域 (`DrawBarcode`)
- 位置: 页面顶部
- 内容: 条形码图形 + 条形码号码
- 右侧: 二维码占位区域

### 2. 标题区域 (`DrawTitle`)
- 居中显示"发货通知单"
- 右上角显示页码和单号信息

### 3. 基本信息区域 (`DrawBasicInfo`)
- 客户信息、联系方式
- 发货信息、交货方式
- 地址和备注信息

### 4. 明细表格区域 (`DrawItemsTable`)
- 表头: 序号、品名规格、单位等
- 数据行: 发运单明细项目
- 边框和分隔线

### 5. 底部信息区域 (`DrawFooterInfo`)
- 统计信息: 重量、数量汇总
- 签名区域: 业务员、主管等
- 打印时间和其他信息

## 🚀 如何查看打印预览

1. **重新构建项目**（已添加预览功能）
2. **重启应用程序**
3. **进入第三步**：选择发运单
4. **点击"打印预览"按钮**：查看完整格式
5. **点击"打印发运单"按钮**：直接打印

## 💡 调试提示

- 当前使用测试数据，包含3个明细项目
- 打印预览可以看到完整的格式布局
- 实际打印会发送到默认打印机
- 支持针式打印机的文本格式

---

**下一步**: 重新构建项目并测试打印预览功能！
