{"format": 1, "restore": {"F:\\printsoft\\ShippingNotePrinter\\ShippingNotePrinter.csproj": {}}, "projects": {"F:\\printsoft\\ShippingNotePrinter\\ShippingNotePrinter.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\printsoft\\ShippingNotePrinter\\ShippingNotePrinter.csproj", "projectName": "ShippingNotePrinter", "projectPath": "F:\\printsoft\\ShippingNotePrinter\\ShippingNotePrinter.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\printsoft\\ShippingNotePrinter\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {}}}}, "frameworks": {"net48": {"dependencies": {"ZXing.Net": {"target": "Package", "version": "[0.16.9, )"}, "ZXing.Net.Bindings.Windows.Compatibility": {"target": "Package", "version": "[0.16.12, )"}}}}, "runtimes": {"win": {"#import": []}}}}}