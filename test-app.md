# 发运单打印系统测试指南

## 🎉 应用程序已成功启动！

应用程序当前正在运行，进程ID: 19552

## 📱 测试步骤

### 第一步：输入手机号
1. 在主界面输入任意11位手机号（例如：13800138000）
2. 点击"发送验证码"按钮
3. 系统会模拟发送验证码（调试模式下总是成功）

### 第二步：输入验证码
1. 输入任意4位以上的数字作为验证码（例如：1234）
2. 点击"验证"按钮
3. 系统会验证验证码（调试模式下4位以上数字都会通过）

### 第三步：选择发运单
1. 验证成功后会显示测试发运单列表
2. 勾选要打印的发运单
3. 点击"打印发运单"按钮

## 🔧 调试功能说明

当前应用程序运行在调试模式下，具有以下特性：

### 短信服务（调试模式）
- ✅ 发送验证码：总是返回成功
- ✅ 验证验证码：任何4位以上数字都通过验证
- ⏱️ 模拟网络延迟：1秒发送，0.5秒验证

### 发运单查询（调试模式）
- ✅ 返回2张测试发运单
- 📋 包含完整的发运单信息
- ⏱️ 模拟网络延迟：1秒查询

### 打印功能
- 🖨️ 会弹出打印对话框
- 📄 按照真实发运单格式设计
- ✅ 支持批量打印

## 🎯 测试重点

1. **界面响应性**：检查触控操作是否流畅
2. **步骤流程**：验证三步流程是否正确
3. **错误处理**：尝试输入无效数据测试错误提示
4. **打印预览**：检查打印格式是否符合要求

## 🔄 重新启动应用

如果需要重新启动应用程序：

```bash
# 结束当前进程
taskkill /PID 19552 /F

# 重新启动
.\ShippingNotePrinter\bin\Debug\ShippingNotePrinter.exe
```

## 📝 测试建议

1. **手机号测试**：
   - 正确格式：13800138000
   - 错误格式：123（应该显示错误提示）

2. **验证码测试**：
   - 正确验证码：1234, 123456
   - 错误验证码：123（少于4位，应该验证失败）

3. **发运单选择**：
   - 不选择任何发运单点击打印（应该显示提示）
   - 选择一张或多张发运单打印

## 🚀 部署准备

当调试完成后，需要：
1. 修改服务配置，连接真实的Tomcat服务
2. 启用真实的SOAP请求代码
3. 配置Oracle数据库连接
4. 配置针式打印机

---

**当前状态**：✅ 应用程序运行中，可以进行功能测试
