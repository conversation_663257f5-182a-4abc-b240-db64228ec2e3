namespace ShippingNotePrinter.Models
{
    /// <summary>
    /// 发运单明细项目模型
    /// </summary>
    public class ShippingNoteItem
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int SequenceNumber { get; set; }

        /// <summary>
        /// 品名规格 (mm)
        /// </summary>
        public string ProductSpecification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单重
        /// </summary>
        public decimal UnitWeight { get; set; }

        /// <summary>
        /// 标长/容器总长(只)数
        /// </summary>
        public decimal StandardLength { get; set; }

        /// <summary>
        /// 总标长
        /// </summary>
        public decimal TotalStandardLength { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// 计算金额
        /// </summary>
        public void CalculateAmount()
        {
            Amount = Quantity * UnitPrice;
        }

        /// <summary>
        /// 计算总标长
        /// </summary>
        public void CalculateTotalStandardLength()
        {
            TotalStandardLength = Quantity * StandardLength;
        }
    }
}
